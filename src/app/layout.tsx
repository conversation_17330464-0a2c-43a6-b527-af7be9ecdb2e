
import type { Metadata } from 'next';
import './globals.css';
// import { Toaster } from "@/components/ui/toaster"; // Removed

export const metadata: Metadata = {
  title: 'UpDown River Scorer',
  description: 'An app for scoring the card game Up and Down the River.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=PT+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased min-h-screen flex flex-col">
        {children}
        {/* <Toaster /> */} {/* Removed */}
      </body>
    </html>
  );
}
